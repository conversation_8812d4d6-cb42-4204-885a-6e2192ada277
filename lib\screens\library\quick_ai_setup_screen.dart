import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/smart_composer_controller.dart';
import '../../controllers/api_config_controller.dart';
import '../../models/smart_composer_models.dart';

/// 快速AI设置界面
/// 用于快速配置AI模型，直接使用现有的API配置
class QuickAISetupScreen extends StatefulWidget {
  const QuickAISetupScreen({super.key});

  @override
  State<QuickAISetupScreen> createState() => _QuickAISetupScreenState();
}

class _QuickAISetupScreenState extends State<QuickAISetupScreen> {
  final SmartComposerController _smartComposerController = Get.find<SmartComposerController>();
  final ApiConfigController _apiConfig = Get.find<ApiConfigController>();

  @override
  void initState() {
    super.initState();
    _syncFromApiConfig();
  }

  /// 从现有API配置同步到Smart Composer
  void _syncFromApiConfig() {
    final providers = <LLMProvider>[];
    final models = <ChatModel>[];

    // 从当前模型配置同步
    final currentModel = _apiConfig.currentModel.value;
    if (currentModel.apiKey.isNotEmpty) {
      LLMProviderType providerType;
      String providerId;

      // 根据模型名称或API URL判断提供商类型
      if (currentModel.name.toLowerCase().contains('openai') ||
          currentModel.name.toLowerCase().contains('gpt') ||
          currentModel.name.toLowerCase().contains('closeai') ||
          currentModel.apiUrl.contains('openai')) {
        providerType = LLMProviderType.openai;
        providerId = 'openai-synced';
      } else if (currentModel.name.toLowerCase().contains('claude') ||
                 currentModel.apiUrl.contains('anthropic')) {
        providerType = LLMProviderType.anthropic;
        providerId = 'claude-synced';
      } else if (currentModel.name.toLowerCase().contains('gemini') ||
                 currentModel.apiUrl.contains('googleapis')) {
        providerType = LLMProviderType.gemini;
        providerId = 'gemini-synced';
      } else if (currentModel.name.toLowerCase().contains('deepseek') ||
                 currentModel.apiUrl.contains('deepseek')) {
        providerType = LLMProviderType.deepseek;
        providerId = 'deepseek-synced';
      } else {
        // 默认使用OpenAI兼容
        providerType = LLMProviderType.openaiCompatible;
        providerId = 'custom-synced';
      }

      final provider = LLMProvider(
        id: providerId,
        type: providerType,
        apiKey: currentModel.apiKey,
        baseUrl: currentModel.apiUrl.isNotEmpty ? currentModel.apiUrl : null,
      );
      providers.add(provider);

      final chatModel = ChatModel(
        id: currentModel.model,
        model: currentModel.model,
        providerId: providerId,
        providerType: providerType,
      );
      models.add(chatModel);
    }

    // 从所有可用模型中同步
    for (final modelConfig in _apiConfig.models) {
      if (modelConfig.apiKey.isNotEmpty && modelConfig != currentModel) {
        LLMProviderType providerType;
        String providerId;

        if (modelConfig.name.toLowerCase().contains('openai') ||
            modelConfig.name.toLowerCase().contains('gpt') ||
            modelConfig.name.toLowerCase().contains('closeai') ||
            modelConfig.apiUrl.contains('openai')) {
          providerType = LLMProviderType.openai;
          providerId = 'openai-${modelConfig.name.toLowerCase().replaceAll(' ', '-')}';
        } else if (modelConfig.name.toLowerCase().contains('claude') ||
                   modelConfig.apiUrl.contains('anthropic')) {
          providerType = LLMProviderType.anthropic;
          providerId = 'claude-${modelConfig.name.toLowerCase().replaceAll(' ', '-')}';
        } else if (modelConfig.name.toLowerCase().contains('gemini') ||
                   modelConfig.apiUrl.contains('googleapis')) {
          providerType = LLMProviderType.gemini;
          providerId = 'gemini-${modelConfig.name.toLowerCase().replaceAll(' ', '-')}';
        } else if (modelConfig.name.toLowerCase().contains('deepseek') ||
                   modelConfig.apiUrl.contains('deepseek')) {
          providerType = LLMProviderType.deepseek;
          providerId = 'deepseek-${modelConfig.name.toLowerCase().replaceAll(' ', '-')}';
        } else {
          providerType = LLMProviderType.openaiCompatible;
          providerId = 'custom-${modelConfig.name.toLowerCase().replaceAll(' ', '-')}';
        }

        // 检查是否已经添加了相同的提供商
        if (!providers.any((p) => p.id == providerId)) {
          final provider = LLMProvider(
            id: providerId,
            type: providerType,
            apiKey: modelConfig.apiKey,
            baseUrl: modelConfig.apiUrl.isNotEmpty ? modelConfig.apiUrl : null,
          );
          providers.add(provider);
        }

        final chatModel = ChatModel(
          id: '${modelConfig.name}-${modelConfig.model}',
          model: modelConfig.model,
          providerId: providerId,
          providerType: providerType,
        );
        models.add(chatModel);
      }
    }

    // 更新Smart Composer配置
    if (providers.isNotEmpty) {
      final currentSettings = _smartComposerController.settings.value;

      // 清除之前同步的配置，避免重复
      final filteredProviders = currentSettings.providers
          .where((p) => !p.id.endsWith('-synced') && !p.id.contains('custom-') && !p.id.contains('openai-') && !p.id.contains('claude-') && !p.id.contains('gemini-') && !p.id.contains('deepseek-'))
          .toList();
      final filteredModels = currentSettings.chatModels
          .where((m) => !m.providerId.endsWith('-synced') && !m.providerId.contains('custom-') && !m.providerId.contains('openai-') && !m.providerId.contains('claude-') && !m.providerId.contains('gemini-') && !m.providerId.contains('deepseek-'))
          .toList();

      final updatedSettings = currentSettings.copyWith(
        providers: [...filteredProviders, ...providers],
        chatModels: [...filteredModels, ...models],
        defaultChatModelId: models.isNotEmpty ? models.first.id : currentSettings.defaultChatModelId,
      );

      _smartComposerController.settings.value = updatedSettings;
      _smartComposerController.saveSettings();

      Get.snackbar('成功', '已同步 ${providers.length} 个AI提供商和 ${models.length} 个模型');
    } else {
      Get.snackbar('提示', '没有找到已配置的API密钥，请先在设置中配置');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI模型配置'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('完成'),
          ),
        ],
      ),
      body: Obx(() => SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatusCard(),
            const SizedBox(height: 16),
            _buildAvailableModelsCard(),
            const SizedBox(height: 16),
            _buildQuickActionsCard(),
          ],
        ),
      )),
    );
  }

  Widget _buildStatusCard() {
    final availableProviders = _smartComposerController.availableProviders
        .where((p) => _smartComposerController.isProviderConfigured(p.id))
        .toList();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  availableProviders.isNotEmpty ? Icons.check_circle : Icons.warning,
                  color: availableProviders.isNotEmpty ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  availableProviders.isNotEmpty ? 'AI模型已配置' : '需要配置AI模型',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (availableProviders.isNotEmpty) ...[
              Text('已配置 ${availableProviders.length} 个AI提供商'),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: availableProviders.map((provider) {
                  return Chip(
                    label: Text(provider.type.displayName),
                    backgroundColor: Colors.green.withOpacity(0.1),
                  );
                }).toList(),
              ),
            ] else ...[
              const Text('请先在设置中配置API密钥，然后点击"同步配置"按钮。'),
              const SizedBox(height: 12),
              ElevatedButton(
                onPressed: () => Get.toNamed('/settings'),
                child: const Text('去设置'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAvailableModelsCard() {
    final availableModels = _smartComposerController.availableModels
        .where((m) => _smartComposerController.isProviderConfigured(m.providerId))
        .toList();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '可用模型',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            if (availableModels.isEmpty) ...[
              const Text('暂无可用模型'),
            ] else ...[
              DropdownButtonFormField<String>(
                value: _smartComposerController.settings.value.defaultChatModelId,
                decoration: const InputDecoration(
                  labelText: '默认模型',
                  border: OutlineInputBorder(),
                ),
                items: availableModels.map((model) {
                  return DropdownMenuItem(
                    value: model.id,
                    child: Text('${model.id} (${model.providerType.displayName})'),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    _smartComposerController.setDefaultModel(value);
                  }
                },
              ),
              const SizedBox(height: 12),
              Text('共 ${availableModels.length} 个可用模型'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '快速操作',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _syncFromApiConfig,
                  icon: const Icon(Icons.sync),
                  label: const Text('同步配置'),
                ),
                ElevatedButton.icon(
                  onPressed: () => Get.toNamed('/smart_composer_settings'),
                  icon: const Icon(Icons.settings),
                  label: const Text('高级设置'),
                ),
                ElevatedButton.icon(
                  onPressed: _testConnection,
                  icon: const Icon(Icons.wifi_tethering),
                  label: const Text('测试连接'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _testConnection() async {
    final defaultModel = _smartComposerController.defaultModel;
    if (defaultModel == null) {
      Get.snackbar('错误', '请先选择默认模型');
      return;
    }

    try {
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      await _smartComposerController.sendMessage(
        content: '你好，请回复"连接成功"',
      );

      Get.back(); // 关闭加载对话框
      Get.snackbar('成功', 'AI模型连接正常');
    } catch (e) {
      Get.back(); // 关闭加载对话框
      Get.snackbar('错误', '连接失败：$e');
    }
  }
}
