import 'dart:io';
import 'dart:convert';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import '../models/novel.dart';
import '../models/chapter.dart';

/// 小说文件管理服务
/// 负责将小说以文件夹+Markdown文件的形式存储到本地文件系统
class NovelFileManager {
  static const String _novelsRootDir = 'novels';
  static const String _metadataFileName = 'metadata.json';
  static const String _outlineFileName = 'outline.md';
  
  /// 获取小说根目录
  Future<Directory> get _novelsDirectory async {
    final appDir = await getApplicationDocumentsDirectory();
    final novelsDir = Directory(path.join(appDir.path, _novelsRootDir));
    if (!await novelsDir.exists()) {
      await novelsDir.create(recursive: true);
    }
    return novelsDir;
  }

  /// 获取小说文件夹路径
  Future<Directory> _getNovelDirectory(String novelId, String novelTitle) async {
    final novelsDir = await _novelsDirectory;
    // 使用安全的文件夹名称
    final safeFolderName = _sanitizeFileName('${novelTitle}_$novelId');
    return Directory(path.join(novelsDir.path, safeFolderName));
  }

  /// 清理文件名，移除不安全字符
  String _sanitizeFileName(String fileName) {
    return fileName
        .replaceAll(RegExp(r'[<>:"/\\|?*]'), '_')
        .replaceAll(RegExp(r'\s+'), '_')
        .trim();
  }

  /// 保存小说到文件系统
  Future<String> saveNovelToFileSystem(Novel novel) async {
    try {
      final novelDir = await _getNovelDirectory(novel.id, novel.title);
      
      // 创建小说文件夹
      if (!await novelDir.exists()) {
        await novelDir.create(recursive: true);
      }

      // 保存元数据
      await _saveMetadata(novelDir, novel);
      
      // 保存大纲
      await _saveOutline(novelDir, novel.outline);
      
      // 保存章节
      await _saveChapters(novelDir, novel.chapters);
      
      return novelDir.path;
    } catch (e) {
      throw Exception('保存小说到文件系统失败: $e');
    }
  }

  /// 保存元数据
  Future<void> _saveMetadata(Directory novelDir, Novel novel) async {
    final metadataFile = File(path.join(novelDir.path, _metadataFileName));
    final metadata = {
      'id': novel.id,
      'title': novel.title,
      'genre': novel.genre,
      'style': novel.style,
      'sessionId': novel.sessionId,
      'createdAt': novel.createdAt.toIso8601String(),
      'updatedAt': novel.updatedAt?.toIso8601String(),
      'wordCount': novel.wordCount,
      'chapterCount': novel.chapters.length,
    };
    
    await metadataFile.writeAsString(
      const JsonEncoder.withIndent('  ').convert(metadata),
      encoding: utf8,
    );
  }

  /// 保存大纲
  Future<void> _saveOutline(Directory novelDir, String outline) async {
    final outlineFile = File(path.join(novelDir.path, _outlineFileName));
    await outlineFile.writeAsString(
      '# 小说大纲\n\n$outline',
      encoding: utf8,
    );
  }

  /// 保存章节
  Future<void> _saveChapters(Directory novelDir, List<Chapter> chapters) async {
    for (final chapter in chapters) {
      await _saveChapter(novelDir, chapter);
    }
  }

  /// 保存单个章节
  Future<void> _saveChapter(Directory novelDir, Chapter chapter) async {
    final chapterFileName = _sanitizeFileName(
      '第${chapter.number.toString().padLeft(3, '0')}章_${chapter.title}.md'
    );
    final chapterFile = File(path.join(novelDir.path, chapterFileName));
    
    final content = '''# 第${chapter.number}章：${chapter.title}

${chapter.content}
''';
    
    await chapterFile.writeAsString(content, encoding: utf8);
  }

  /// 从文件系统加载小说
  Future<Novel?> loadNovelFromFileSystem(String folderPath) async {
    try {
      final novelDir = Directory(folderPath);
      if (!await novelDir.exists()) {
        return null;
      }

      // 加载元数据
      final metadata = await _loadMetadata(novelDir);
      if (metadata == null) {
        return null;
      }

      // 加载大纲
      final outline = await _loadOutline(novelDir);
      
      // 加载章节
      final chapters = await _loadChapters(novelDir);
      
      return Novel(
        id: metadata['id'] as String,
        title: metadata['title'] as String,
        genre: metadata['genre'] as String,
        outline: outline,
        content: chapters.map((c) => c.content).join('\n\n'),
        chapters: chapters,
        createdAt: DateTime.parse(metadata['createdAt'] as String),
        updatedAt: metadata['updatedAt'] != null 
            ? DateTime.parse(metadata['updatedAt'] as String) 
            : null,
        style: metadata['style'] as String?,
        sessionId: metadata['sessionId'] as String?,
        folderPath: folderPath,
        useFileSystem: true,
      );
    } catch (e) {
      print('加载小说失败: $e');
      return null;
    }
  }

  /// 加载元数据
  Future<Map<String, dynamic>?> _loadMetadata(Directory novelDir) async {
    final metadataFile = File(path.join(novelDir.path, _metadataFileName));
    if (!await metadataFile.exists()) {
      return null;
    }
    
    final content = await metadataFile.readAsString(encoding: utf8);
    return json.decode(content) as Map<String, dynamic>;
  }

  /// 加载大纲
  Future<String> _loadOutline(Directory novelDir) async {
    final outlineFile = File(path.join(novelDir.path, _outlineFileName));
    if (!await outlineFile.exists()) {
      return '';
    }
    
    final content = await outlineFile.readAsString(encoding: utf8);
    // 移除Markdown标题
    return content.replaceFirst(RegExp(r'^# 小说大纲\s*\n\n?'), '');
  }

  /// 加载章节
  Future<List<Chapter>> _loadChapters(Directory novelDir) async {
    final chapters = <Chapter>[];
    
    await for (final entity in novelDir.list()) {
      if (entity is File && entity.path.endsWith('.md')) {
        final fileName = path.basename(entity.path);
        if (fileName != _outlineFileName) {
          final chapter = await _loadChapter(entity);
          if (chapter != null) {
            chapters.add(chapter);
          }
        }
      }
    }
    
    // 按章节号排序
    chapters.sort((a, b) => a.number.compareTo(b.number));
    return chapters;
  }

  /// 加载单个章节
  Future<Chapter?> _loadChapter(File chapterFile) async {
    try {
      final content = await chapterFile.readAsString(encoding: utf8);
      final lines = content.split('\n');
      
      if (lines.isEmpty) return null;
      
      // 解析标题行
      final titleLine = lines.first;
      final titleMatch = RegExp(r'^# 第(\d+)章：(.+)$').firstMatch(titleLine);
      
      if (titleMatch == null) return null;
      
      final chapterNumber = int.parse(titleMatch.group(1)!);
      final chapterTitle = titleMatch.group(2)!;
      
      // 获取内容（跳过标题行和空行）
      final chapterContent = lines
          .skip(1)
          .skipWhile((line) => line.trim().isEmpty)
          .join('\n')
          .trim();
      
      return Chapter(
        number: chapterNumber,
        title: chapterTitle,
        content: chapterContent,
      );
    } catch (e) {
      print('加载章节失败: $e');
      return null;
    }
  }

  /// 更新章节内容
  Future<void> updateChapter(String folderPath, Chapter chapter) async {
    final novelDir = Directory(folderPath);
    await _saveChapter(novelDir, chapter);
    
    // 更新元数据的修改时间
    await _updateMetadataTimestamp(novelDir);
  }

  /// 更新元数据时间戳
  Future<void> _updateMetadataTimestamp(Directory novelDir) async {
    final metadataFile = File(path.join(novelDir.path, _metadataFileName));
    if (await metadataFile.exists()) {
      final content = await metadataFile.readAsString(encoding: utf8);
      final metadata = json.decode(content) as Map<String, dynamic>;
      metadata['updatedAt'] = DateTime.now().toIso8601String();
      
      await metadataFile.writeAsString(
        const JsonEncoder.withIndent('  ').convert(metadata),
        encoding: utf8,
      );
    }
  }

  /// 删除小说文件夹
  Future<void> deleteNovel(String folderPath) async {
    final novelDir = Directory(folderPath);
    if (await novelDir.exists()) {
      await novelDir.delete(recursive: true);
    }
  }

  /// 获取所有小说文件夹
  Future<List<String>> getAllNovelFolders() async {
    final novelsDir = await _novelsDirectory;
    final folders = <String>[];
    
    await for (final entity in novelsDir.list()) {
      if (entity is Directory) {
        final metadataFile = File(path.join(entity.path, _metadataFileName));
        if (await metadataFile.exists()) {
          folders.add(entity.path);
        }
      }
    }
    
    return folders;
  }

  /// 导出小说为单个Markdown文件
  Future<String> exportNovelAsMarkdown(Novel novel) async {
    final buffer = StringBuffer();
    
    // 标题和元信息
    buffer.writeln('# ${novel.title}');
    buffer.writeln();
    buffer.writeln('**类型**: ${novel.genre}');
    if (novel.style?.isNotEmpty == true) {
      buffer.writeln('**风格**: ${novel.style}');
    }
    buffer.writeln('**创建时间**: ${novel.createTime}');
    buffer.writeln('**字数**: ${novel.wordCount}');
    buffer.writeln();
    
    // 大纲
    if (novel.outline.isNotEmpty) {
      buffer.writeln('## 大纲');
      buffer.writeln();
      buffer.writeln(novel.outline);
      buffer.writeln();
    }
    
    // 章节内容
    for (final chapter in novel.chapters) {
      buffer.writeln('## 第${chapter.number}章：${chapter.title}');
      buffer.writeln();
      buffer.writeln(chapter.content);
      buffer.writeln();
    }
    
    return buffer.toString();
  }
}
