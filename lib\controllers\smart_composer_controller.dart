import 'package:get/get.dart';
import 'package:hive/hive.dart';
import '../models/smart_composer_models.dart';
import '../models/novel.dart';
import '../models/chapter.dart';
import '../services/smart_composer_service.dart';

/// Smart Composer 控制器
/// 管理 AI 写作助手的状态和配置
class SmartComposerController extends GetxController {
  static const String _settingsBoxName = 'smart_composer_settings';
  static const String _sessionsBoxName = 'smart_composer_sessions';
  
  late Box _settingsBox;
  late Box _sessionsBox;
  
  final SmartComposerService _service = SmartComposerService();
  
  // 响应式状态
  final Rx<SmartComposerSettings> settings = SmartComposerSettings(
    providers: SmartComposerDefaults.defaultProviders,
    chatModels: SmartComposerDefaults.defaultChatModels,
    defaultChatModelId: SmartComposerDefaults.defaultChatModels.first.id,
    systemPrompt: SmartComposerDefaults.defaultSystemPrompt,
  ).obs;
  
  final RxList<ChatSession> chatSessions = <ChatSession>[].obs;
  final Rx<ChatSession?> currentSession = Rx<ChatSession?>(null);
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeBoxes();
    await _loadSettings();
    await _loadChatSessions();
  }

  /// 初始化 Hive 存储盒子
  Future<void> _initializeBoxes() async {
    _settingsBox = await Hive.openBox(_settingsBoxName);
    _sessionsBox = await Hive.openBox(_sessionsBoxName);
  }

  /// 加载设置
  Future<void> _loadSettings() async {
    try {
      final settingsData = _settingsBox.get('settings');
      if (settingsData != null) {
        settings.value = SmartComposerSettings.fromJson(
          Map<String, dynamic>.from(settingsData as Map)
        );
      }
    } catch (e) {
      print('加载 Smart Composer 设置失败: $e');
    }
  }

  /// 保存设置
  Future<void> saveSettings() async {
    try {
      await _settingsBox.put('settings', settings.value.toJson());
    } catch (e) {
      print('保存 Smart Composer 设置失败: $e');
      error.value = '保存设置失败: $e';
    }
  }

  /// 加载聊天会话
  Future<void> _loadChatSessions() async {
    try {
      final sessions = <ChatSession>[];
      for (final key in _sessionsBox.keys) {
        final sessionData = _sessionsBox.get(key);
        if (sessionData != null) {
          final session = ChatSession.fromJson(
            Map<String, dynamic>.from(sessionData as Map)
          );
          sessions.add(session);
        }
      }
      
      // 按创建时间排序
      sessions.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      chatSessions.value = sessions;
    } catch (e) {
      print('加载聊天会话失败: $e');
    }
  }

  /// 保存聊天会话
  Future<void> _saveChatSession(ChatSession session) async {
    try {
      await _sessionsBox.put(session.id, session.toJson());
      
      // 更新本地列表
      final index = chatSessions.indexWhere((s) => s.id == session.id);
      if (index >= 0) {
        chatSessions[index] = session;
      } else {
        chatSessions.insert(0, session);
      }
    } catch (e) {
      print('保存聊天会话失败: $e');
      error.value = '保存会话失败: $e';
    }
  }

  /// 创建新的聊天会话
  ChatSession createNewSession({
    String? title,
    String? novelId,
    int? chapterNumber,
    Map<String, dynamic>? context,
  }) {
    final session = ChatSession(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title ?? '新对话',
      messages: [],
      createdAt: DateTime.now(),
      novelId: novelId,
      chapterNumber: chapterNumber,
      context: context,
    );
    
    currentSession.value = session;
    return session;
  }

  /// 发送消息
  Future<void> sendMessage({
    required String content,
    Novel? novel,
    Chapter? chapter,
  }) async {
    if (currentSession.value == null) {
      error.value = '没有活动的聊天会话';
      return;
    }

    try {
      isLoading.value = true;
      error.value = '';

      // 获取当前模型和提供商
      final modelId = settings.value.defaultChatModelId;
      if (modelId == null) {
        throw Exception('未配置默认模型');
      }

      final model = settings.value.chatModels.firstWhere(
        (m) => m.id == modelId,
        orElse: () => throw Exception('找不到指定的模型'),
      );

      final provider = settings.value.providers.firstWhere(
        (p) => p.id == model.providerId,
        orElse: () => throw Exception('找不到指定的提供商'),
      );

      // 创建用户消息
      final userMessage = _service.createUserMessage(content);
      
      // 更新会话
      final updatedMessages = List<ChatMessage>.from(currentSession.value!.messages)
        ..add(userMessage);
      
      currentSession.value = currentSession.value!.copyWith(
        messages: updatedMessages,
        updatedAt: DateTime.now(),
      );

      // 生成系统提示
      String? systemPrompt = settings.value.systemPrompt;
      if (novel != null) {
        final contextPrompt = _service.generateNovelContextPrompt(novel, chapter);
        systemPrompt = '${systemPrompt ?? ''}\n\n$contextPrompt';
      }

      // 发送到 AI 服务
      final response = await _service.sendChatMessage(
        model: model,
        provider: provider,
        messages: updatedMessages,
        systemPrompt: systemPrompt,
      );

      // 创建助手回复
      final assistantMessage = _service.createAssistantMessage(response);
      
      // 更新会话
      final finalMessages = List<ChatMessage>.from(updatedMessages)
        ..add(assistantMessage);
      
      currentSession.value = currentSession.value!.copyWith(
        messages: finalMessages,
        updatedAt: DateTime.now(),
      );

      // 保存会话
      await _saveChatSession(currentSession.value!);

    } catch (e) {
      error.value = '发送消息失败: $e';
      print('发送消息失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// 添加提供商
  void addProvider(LLMProvider provider) {
    final updatedProviders = List<LLMProvider>.from(settings.value.providers)
      ..add(provider);
    
    settings.value = settings.value.copyWith(providers: updatedProviders);
    saveSettings();
  }

  /// 更新提供商
  void updateProvider(LLMProvider provider) {
    final updatedProviders = settings.value.providers.map((p) {
      return p.id == provider.id ? provider : p;
    }).toList();
    
    settings.value = settings.value.copyWith(providers: updatedProviders);
    saveSettings();
  }

  /// 删除提供商
  void removeProvider(String providerId) {
    final updatedProviders = settings.value.providers
        .where((p) => p.id != providerId)
        .toList();
    
    settings.value = settings.value.copyWith(providers: updatedProviders);
    saveSettings();
  }

  /// 添加聊天模型
  void addChatModel(ChatModel model) {
    final updatedModels = List<ChatModel>.from(settings.value.chatModels)
      ..add(model);
    
    settings.value = settings.value.copyWith(chatModels: updatedModels);
    saveSettings();
  }

  /// 更新聊天模型
  void updateChatModel(ChatModel model) {
    final updatedModels = settings.value.chatModels.map((m) {
      return m.id == model.id ? model : m;
    }).toList();
    
    settings.value = settings.value.copyWith(chatModels: updatedModels);
    saveSettings();
  }

  /// 删除聊天模型
  void removeChatModel(String modelId) {
    final updatedModels = settings.value.chatModels
        .where((m) => m.id != modelId)
        .toList();
    
    settings.value = settings.value.copyWith(chatModels: updatedModels);
    saveSettings();
  }

  /// 设置默认模型
  void setDefaultModel(String modelId) {
    settings.value = settings.value.copyWith(defaultChatModelId: modelId);
    saveSettings();
  }

  /// 更新系统提示
  void updateSystemPrompt(String prompt) {
    settings.value = settings.value.copyWith(systemPrompt: prompt);
    saveSettings();
  }

  /// 删除聊天会话
  Future<void> deleteChatSession(String sessionId) async {
    try {
      await _sessionsBox.delete(sessionId);
      chatSessions.removeWhere((s) => s.id == sessionId);
      
      if (currentSession.value?.id == sessionId) {
        currentSession.value = null;
      }
    } catch (e) {
      error.value = '删除会话失败: $e';
    }
  }

  /// 切换到指定会话
  void switchToSession(ChatSession session) {
    currentSession.value = session;
  }

  /// 获取可用的提供商
  List<LLMProvider> get availableProviders => settings.value.providers;

  /// 获取可用的模型
  List<ChatModel> get availableModels => settings.value.chatModels;

  /// 获取当前默认模型
  ChatModel? get defaultModel {
    final modelId = settings.value.defaultChatModelId;
    if (modelId == null) return null;
    
    try {
      return settings.value.chatModels.firstWhere((m) => m.id == modelId);
    } catch (e) {
      return null;
    }
  }

  /// 检查提供商是否已配置
  bool isProviderConfigured(String providerId) {
    final provider = settings.value.providers
        .where((p) => p.id == providerId)
        .firstOrNull;
    
    if (provider == null) return false;
    
    // 检查是否需要 API Key
    switch (provider.type) {
      case LLMProviderType.openai:
      case LLMProviderType.anthropic:
      case LLMProviderType.gemini:
      case LLMProviderType.deepseek:
        return provider.apiKey != null && provider.apiKey!.isNotEmpty;
      case LLMProviderType.ollama:
      case LLMProviderType.lmStudio:
        return true; // 本地模型不需要 API Key
      default:
        return provider.apiKey != null && provider.apiKey!.isNotEmpty;
    }
  }

  @override
  void onClose() {
    _settingsBox.close();
    _sessionsBox.close();
    super.onClose();
  }
}
